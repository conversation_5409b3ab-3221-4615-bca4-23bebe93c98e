#include "SpeechRecognizer.h"
#include "whisper.h"
#include <iostream>
#include <fstream>
#include <cstring>
#include <algorithm>
#include <vector>
#include <cctype>
#include <sstream>
#ifdef _WIN32
#include <windows.h>
#endif

SpeechRecognizer::SpeechRecognizer()
    : ctx_(nullptr), shouldStop_(false)
{
}

SpeechRecognizer::~SpeechRecognizer()
{
    Shutdown();
}

bool SpeechRecognizer::Initialize(const Config &config)
{
    config_ = config;

    // Load the whisper model
    std::string modelPath = GetModelPath(config.model);

    // Debug output
    std::cout << "Looking for model at: " << modelPath << std::endl;

#ifdef _WIN32
    // Also try to get current working directory
    char cwd[MAX_PATH];
    if (GetCurrentDirectoryA(MAX_PATH, cwd))
    {
        std::cout << "Current working directory: " << cwd << std::endl;
    }
#endif

    // Check if model file exists
    std::ifstream modelFile(modelPath, std::ios::binary);
    if (!modelFile.good())
    {
        std::cerr << "Model file not found: " << modelPath << std::endl;

        // Try alternative paths
        std::vector<std::string> alternativePaths = {
            "models/ggml-tiny.bin",
            "./models/ggml-tiny.bin",
            "../models/ggml-tiny.bin",
            "../../models/ggml-tiny.bin"};

        std::cout << "Trying alternative paths:" << std::endl;
        for (const auto &altPath : alternativePaths)
        {
            std::cout << "  Checking: " << altPath << " ... ";
            std::ifstream altFile(altPath, std::ios::binary);
            if (altFile.good())
            {
                std::cout << "FOUND!" << std::endl;
                altFile.close();
                modelPath = altPath;
                modelFile.open(modelPath, std::ios::binary);
                break;
            }
            else
            {
                std::cout << "not found" << std::endl;
            }
        }

        if (!modelFile.good())
        {
            return false;
        }
    }
    modelFile.close();

    // Initialize whisper context with model
    struct whisper_context_params cparams = whisper_context_default_params();
    ctx_ = whisper_init_from_file_with_params(modelPath.c_str(), cparams);

    if (!ctx_)
    {
        std::cerr << "Failed to load whisper model from: " << modelPath << std::endl;
        return false;
    }

    std::cout << "Successfully loaded whisper model: " << modelPath << std::endl;

    // Start processing thread
    shouldStop_ = false;
    processingThread_ = std::thread(&SpeechRecognizer::ProcessingThread, this);

    return true;
}

void SpeechRecognizer::Shutdown()
{
    shouldStop_ = true;
    queueCV_.notify_all();

    if (processingThread_.joinable())
    {
        processingThread_.join();
    }

    if (ctx_)
    {
        whisper_free(ctx_);
        ctx_ = nullptr;
    }
}

void SpeechRecognizer::ProcessAudio(const std::vector<float> &audioData)
{
    std::cout << "SpeechRecognizer::ProcessAudio: Received " << audioData.size() << " samples" << std::endl;
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        audioQueue_.push(audioData);
        std::cout << "SpeechRecognizer: Audio queued, queue size now: " << audioQueue_.size() << std::endl;
    }
    queueCV_.notify_one();
}

bool SpeechRecognizer::LoadModel(const std::string &modelPath)
{
    // Free existing context if any
    if (ctx_)
    {
        whisper_free(ctx_);
        ctx_ = nullptr;
    }

    // Check if model file exists
    std::ifstream modelFile(modelPath, std::ios::binary);
    if (!modelFile.good())
    {
        std::cerr << "Model file not found: " << modelPath << std::endl;
        return false;
    }
    modelFile.close();

    // Load new model
    struct whisper_context_params cparams = whisper_context_default_params();
    ctx_ = whisper_init_from_file_with_params(modelPath.c_str(), cparams);

    if (!ctx_)
    {
        std::cerr << "Failed to load whisper model from: " << modelPath << std::endl;
        return false;
    }

    std::cout << "Successfully loaded model from: " << modelPath << std::endl;
    return true;
}

std::string SpeechRecognizer::GetModelPath(Model model) const
{
    std::string modelName;
    switch (model)
    {
    case Model::TINY:
        modelName = "ggml-tiny.bin";
        break;
    case Model::BASE:
        modelName = "ggml-base.bin";
        break;
    case Model::SMALL:
        modelName = "ggml-small.bin";
        break;
    case Model::MEDIUM:
        modelName = "ggml-medium.bin";
        break;
    case Model::LARGE:
        modelName = "ggml-large.bin";
        break;
    default:
        modelName = "ggml-tiny.bin";
        break;
    }

#ifdef _WIN32
    // Get the directory where the executable is located
    char buffer[MAX_PATH];
    GetModuleFileNameA(NULL, buffer, MAX_PATH);
    std::string exePath(buffer);
    std::string exeDir = exePath.substr(0, exePath.find_last_of("\\/"));

    // Check if we're running from build/Release
    if (exeDir.find("build\\Release") != std::string::npos ||
        exeDir.find("build/Release") != std::string::npos)
    {
        // Go up two directories to reach project root
        return exeDir + "/../../models/" + modelName;
    }
    else
    {
        // We're in project root
        return "models/" + modelName;
    }
#else
    return "models/" + modelName;
#endif
}

void SpeechRecognizer::ProcessingThread()
{
    std::cout << "=== SpeechRecognizer ProcessingThread started ===" << std::endl;

    while (!shouldStop_)
    {
        std::unique_lock<std::mutex> lock(queueMutex_);
        queueCV_.wait(lock, [this]
                      { return !audioQueue_.empty() || shouldStop_; });

        std::cout << "SpeechRecognizer ProcessingThread: Woke up, queue size=" << audioQueue_.size() << std::endl;

        while (!audioQueue_.empty() && !shouldStop_)
        {
            auto audioData = audioQueue_.front();
            audioQueue_.pop();
            std::cout << "SpeechRecognizer: Processing audio chunk, size=" << audioData.size() << std::endl;
            lock.unlock();

            // SIMPLIFIED APPROACH: Process each audio chunk individually
            // No accumulation, no complex buffering - just process what we get
            if (ctx_ && !audioData.empty())
            {
                // Check if audio has sufficient amplitude
                float maxVal = 0.0f;
                for (float sample : audioData)
                {
                    float absSample = std::abs(sample);
                    if (absSample > maxVal)
                        maxVal = absSample;
                }

                std::cout << "=== PROCESSING INDIVIDUAL CHUNK: " << audioData.size() << " samples, maxVal=" << maxVal << " ===" << std::endl;

                // Only process if there's actual audio content
                if (maxVal > 0.0001f) // Low threshold for low-gain mics
                {
                    std::cout << "✓ AMPLITUDE CHECK PASSED - Processing audio..." << std::endl;

                    // Create whisper parameters
                    struct whisper_full_params wparams = whisper_full_default_params(WHISPER_SAMPLING_GREEDY);

                    // Configure parameters based on RealtimeSTT approach
                    wparams.print_progress = false;
                    wparams.print_special = false;
                    wparams.print_realtime = false;
                    wparams.print_timestamps = false;
                    wparams.translate = false;
                    wparams.no_context = true;
                    wparams.single_segment = false;
                    wparams.max_tokens = 0;
                    wparams.language = config_.language.c_str();
                    wparams.n_threads = config_.threads;

                    // Optimized settings for tiny model - faster and more reliable
                    wparams.suppress_blank = true;
                    wparams.suppress_nst = false; // Allow non-speech tokens for better accuracy
                    wparams.temperature = 0.0f;   // Deterministic results
                    wparams.entropy_thold = 2.0f; // Lower threshold for tiny model
                    wparams.logprob_thold = -1.0f;
                    wparams.no_speech_thold = 0.4f; // Lower threshold for better detection
                    wparams.max_initial_ts = 1.0f;

                    // Additional quality settings
                    wparams.token_timestamps = false; // Don't need timestamps
                    wparams.split_on_word = true;     // Better word boundaries
                    wparams.max_len = 0;              // No max length

                    // Use greedy sampling for more consistent results
                    wparams.strategy = WHISPER_SAMPLING_GREEDY;

                    if (wparams.strategy == WHISPER_SAMPLING_BEAM_SEARCH)
                    {
                        wparams.beam_search.beam_size = config_.beamSize;
                    }

                    // Process audio with whisper
                    std::cout << "=== CALLING WHISPER: " << audioData.size() << " samples, maxVal=" << maxVal << " ===" << std::endl;
                    int result = whisper_full(ctx_, wparams, audioData.data(), audioData.size());
                    std::cout << "=== WHISPER RETURNED: result=" << result << " ===" << std::endl;

                    if (result == 0)
                    {
                        // Get number of segments
                        const int n_segments = whisper_full_n_segments(ctx_);

                        // Concatenate all segments
                        std::string fullText;
                        for (int i = 0; i < n_segments; ++i)
                        {
                            const char *text = whisper_full_get_segment_text(ctx_, i);
                            if (text && strlen(text) > 0)
                            {
                                if (!fullText.empty())
                                {
                                    fullText += " ";
                                }
                                fullText += text;
                            }
                        }

                        // Trim whitespace
                        fullText.erase(0, fullText.find_first_not_of(" \t\n\r"));
                        fullText.erase(fullText.find_last_not_of(" \t\n\r") + 1);

                        // Additional filtering
                        if (!fullText.empty())
                        {
                            // Convert to lowercase for checking
                            std::string lowerText = fullText;
                            std::transform(lowerText.begin(), lowerText.end(), lowerText.begin(), ::tolower);

                            // Common Whisper hallucinations
                            static const std::vector<std::string> hallucinations = {
                                "thank you", "thank you.", "thank you!",
                                "thanks", "thanks.", "thanks!",
                                "you", "you.", "you!",
                                "bye", "bye.", "bye!",
                                "goodbye", "goodbye.", "goodbye!",
                                "hello", "hello.", "hello!",
                                "hi", "hi.", "hi!",
                                "okay", "okay.", "okay!",
                                "oh", "oh.", "oh!",
                                "um", "um.", "uh", "uh.",
                                ".", "..", "...",
                                " ", "",
                                "so", "so.", "well", "well.",
                                "right", "right.", "yes", "yes.",
                                "no", "no.", "yeah", "yeah.",
                                "a", "a.", "i", "i.",
                                "the", "the.", "and", "and.",
                                "it", "it.", "is", "is.",
                                "that", "that.", "this", "this."};

                            bool isValid = true;

                            // Check if it's a known hallucination
                            for (const auto &h : hallucinations)
                            {
                                if (lowerText == h)
                                {
                                    isValid = false;
                                    std::cout << "Filtered hallucination: " << fullText << std::endl;
                                    break;
                                }
                            }

                            // Filter very short text (less strict)
                            if (isValid && fullText.length() < 2)
                            {
                                isValid = false;
                                std::cout << "Filtered too short: " << fullText << std::endl;
                            }

                            // Filter if it contains only common words
                            if (isValid)
                            {
                                std::string temp = lowerText;
                                // Remove punctuation
                                temp.erase(std::remove_if(temp.begin(), temp.end(),
                                                          [](char c)
                                                          { return !std::isalnum(c) && c != ' '; }),
                                           temp.end());

                                // Count meaningful words (not in hallucination list)
                                int meaningfulWords = 0;
                                std::istringstream iss(temp);
                                std::string word;
                                while (iss >> word)
                                {
                                    bool isCommon = false;
                                    for (const auto &h : hallucinations)
                                    {
                                        if (word == h)
                                        {
                                            isCommon = true;
                                            break;
                                        }
                                    }
                                    if (!isCommon)
                                    {
                                        meaningfulWords++;
                                    }
                                }

                                if (meaningfulWords == 0)
                                {
                                    isValid = false;
                                    std::cout << "Filtered only common words: " << fullText << std::endl;
                                }
                            }

                            // Send transcription if valid
                            if (isValid && callback_)
                            {
                                std::cout << "*** TRANSCRIPTION SUCCESS: '" << fullText << "' ***" << std::endl;
                                callback_(fullText, true);
                            }
                            else if (!isValid)
                            {
                                std::cout << "TRANSCRIPTION FILTERED: '" << fullText << "' (likely hallucination)" << std::endl;
                            }
                            else if (!callback_)
                            {
                                std::cout << "ERROR: No callback set for transcription: '" << fullText << "'" << std::endl;
                            }
                        }
                        else
                        {
                            std::cout << "WHISPER: No transcription text returned" << std::endl;
                        }
                    }
                    else
                    {
                        std::cout << "WHISPER ERROR: Processing failed, result=" << result << std::endl;
                    }

                } // end if (maxVal > 0.0001f)
                else
                {
                    std::cout << "✗ AMPLITUDE CHECK FAILED - Audio too quiet (maxVal=" << maxVal << " < 0.0001)" << std::endl;
                }
            } // end if (ctx_ && !audioData.empty())

            lock.lock();
        }
    }
}

void SpeechRecognizer::SetLanguage(const std::string &language)
{
    config_.language = language;
}

void SpeechRecognizer::SetBeamSize(int beamSize)
{
    config_.beamSize = beamSize;
}

void SpeechRecognizer::SetThreads(int threads)
{
    config_.threads = threads;
}