

####### Expanded from @GGML_VARIABLES_EXPANED@ by configure_package_config_file() #######
####### Any changes to this file will be overwritten by the next CMake run        #######

set(GGML_ACCELERATE "ON")
set(GGML_ALL_WARNINGS "ON")
set(GGML_ALL_WARNINGS_3RD_PARTY "OFF")
set(GGML_AMX_BF16 "OFF")
set(GGML_AMX_INT8 "OFF")
set(GGML_AMX_TILE "OFF")
set(GGML_AVAILABLE_BACKENDS "ggml-cpu")
set(GGML_AVX "OFF")
set(GGML_AVX2 "OFF")
set(GGML_AVX512 "OFF")
set(GGML_AVX512_BF16 "OFF")
set(GGML_AVX512_VBMI "OFF")
set(GGML_AVX512_VNNI "OFF")
set(GGML_AVX_VNNI "OFF")
set(GGML_BACKEND_DL "OFF")
set(GGML_BLAS "OFF")
set(GGML_BLAS_DEFAULT "OFF")
set(GGML_BLAS_VENDOR "Generic")
set(GGML_BLAS_VENDOR_DEFAULT "Generic")
set(GGML_BMI2 "OFF")
set(GGML_BUILD_COMMIT "13d92d08")
set(GGML_BUILD_EXAMPLES "OFF")
set(GGML_BUILD_NUMBER "2630")
set(GGML_BUILD_TESTS "OFF")
set(GGML_CCACHE "ON")
set(GGML_CCACHE_FOUND "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/BrechtSanders.WinLibs.POSIX.UCRT_Microsoft.Winget.Source_8wekyb3d8bbwe/mingw64/bin/ccache.exe")
set(GGML_COMPILER_SUPPORTS_FP16_FORMAT_I3E "")
set(GGML_CPU "ON")
set(GGML_CPU_AARCH64 "ON")
set(GGML_CPU_ALL_VARIANTS "OFF")
set(GGML_CPU_ARM_ARCH "")
set(GGML_CPU_HBM "OFF")
set(GGML_CPU_KLEIDIAI "OFF")
set(GGML_CPU_POWERPC_CPUTYPE "")
set(GGML_CUDA "OFF")
set(GGML_CUDA_COMPRESSION_MODE "size")
set(GGML_CUDA_F16 "OFF")
set(GGML_CUDA_FA "ON")
set(GGML_CUDA_FA_ALL_QUANTS "OFF")
set(GGML_CUDA_FORCE_CUBLAS "OFF")
set(GGML_CUDA_FORCE_MMQ "OFF")
set(GGML_CUDA_GRAPHS "OFF")
set(GGML_CUDA_GRAPHS_DEFAULT "OFF")
set(GGML_CUDA_NO_PEER_COPY "OFF")
set(GGML_CUDA_NO_VMM "OFF")
set(GGML_CUDA_PEER_MAX_BATCH_SIZE "128")
set(GGML_F16C "OFF")
set(GGML_FATAL_WARNINGS "OFF")
set(GGML_FMA "OFF")
set(GGML_GPROF "OFF")
set(GGML_HIP "OFF")
set(GGML_HIP_GRAPHS "OFF")
set(GGML_HIP_NO_VMM "ON")
set(GGML_HIP_ROCWMMA_FATTN "OFF")
set(GGML_KOMPUTE "OFF")
set(GGML_LASX "ON")
set(GGML_LLAMAFILE "OFF")
set(GGML_LLAMAFILE_DEFAULT "OFF")
set(GGML_LSX "ON")
set(GGML_LTO "OFF")
set(GGML_MACHINE_SUPPORTS_dotprod "")
set(GGML_MACHINE_SUPPORTS_dotprod_COMPILED "FALSE")
set(GGML_MACHINE_SUPPORTS_i8mm "")
set(GGML_MACHINE_SUPPORTS_i8mm_COMPILED "FALSE")
set(GGML_MACHINE_SUPPORTS_nodotprod "")
set(GGML_MACHINE_SUPPORTS_noi8mm "")
set(GGML_MACHINE_SUPPORTS_nosme "")
set(GGML_MACHINE_SUPPORTS_nosve "")
set(GGML_MACHINE_SUPPORTS_sme "")
set(GGML_MACHINE_SUPPORTS_sme_COMPILED "FALSE")
set(GGML_MACHINE_SUPPORTS_sve "")
set(GGML_MACHINE_SUPPORTS_sve_COMPILED "FALSE")
set(GGML_METAL "OFF")
set(GGML_METAL_DEFAULT "OFF")
set(GGML_METAL_EMBED_LIBRARY "OFF")
set(GGML_METAL_MACOSX_VERSION_MIN "")
set(GGML_METAL_NDEBUG "OFF")
set(GGML_METAL_SHADER_DEBUG "OFF")
set(GGML_METAL_STD "")
set(GGML_METAL_USE_BF16 "OFF")
set(GGML_MUSA "OFF")
set(GGML_NATIVE "ON")
set(GGML_NATIVE_DEFAULT "ON")
set(GGML_OPENCL "OFF")
set(GGML_OPENCL_EMBED_KERNELS "ON")
set(GGML_OPENCL_PROFILING "OFF")
set(GGML_OPENCL_TARGET_VERSION "300")
set(GGML_OPENCL_USE_ADRENO_KERNELS "ON")
set(GGML_OPENMP "ON")
set(GGML_PUBLIC_HEADERS "include/ggml.h;include/ggml-cpu.h;include/ggml-alloc.h;include/ggml-backend.h;include/ggml-blas.h;include/ggml-cann.h;include/ggml-cpp.h;include/ggml-cuda.h;include/ggml-kompute.h;include/ggml-opt.h;include/ggml-metal.h;include/ggml-rpc.h;include/ggml-sycl.h;include/ggml-vulkan.h;include/gguf.h")
set(GGML_RPC "OFF")
set(GGML_RVV "ON")
set(GGML_RV_ZFH "OFF")
set(GGML_SANITIZE_ADDRESS "OFF")
set(GGML_SANITIZE_THREAD "OFF")
set(GGML_SANITIZE_UNDEFINED "OFF")
set(GGML_SCCACHE_FOUND "GGML_SCCACHE_FOUND-NOTFOUND")
set(GGML_SCHED_MAX_COPIES "4")
set(GGML_SHARED_LIB "OFF")
set(GGML_SSE42 "OFF")
set(GGML_STANDALONE "OFF")
set(GGML_STATIC "OFF")
set(GGML_SYCL "OFF")
set(GGML_SYCL_DEVICE_ARCH "")
set(GGML_SYCL_DNN "ON")
set(GGML_SYCL_F16 "OFF")
set(GGML_SYCL_GRAPH "ON")
set(GGML_SYCL_TARGET "INTEL")
set(GGML_VULKAN "OFF")
set(GGML_VULKAN_CHECK_RESULTS "OFF")
set(GGML_VULKAN_DEBUG "OFF")
set(GGML_VULKAN_MEMORY_DEBUG "OFF")
set(GGML_VULKAN_PERF "OFF")
set(GGML_VULKAN_RUN_TESTS "OFF")
set(GGML_VULKAN_SHADERS_GEN_TOOLCHAIN "")
set(GGML_VULKAN_SHADER_DEBUG_INFO "OFF")
set(GGML_VULKAN_VALIDATE "OFF")
set(GGML_VXE "ON")
set(GGML_WIN_VER "0x602")



####### Expanded from @PACKAGE_INIT@ by configure_package_config_file() #######
####### Any changes to this file will be overwritten by the next CMake run ####
####### The input file was ggml-config.cmake.in                            ########

get_filename_component(PACKAGE_PREFIX_DIR "${CMAKE_CURRENT_LIST_DIR}/../../../" ABSOLUTE)

macro(set_and_check _var _file)
  set(${_var} "${_file}")
  if(NOT EXISTS "${_file}")
    message(FATAL_ERROR "File or directory ${_file} referenced by variable ${_var} does not exist !")
  endif()
endmacro()

macro(check_required_components _NAME)
  foreach(comp ${${_NAME}_FIND_COMPONENTS})
    if(NOT ${_NAME}_${comp}_FOUND)
      if(${_NAME}_FIND_REQUIRED_${comp})
        set(${_NAME}_FOUND FALSE)
      endif()
    endif()
  endforeach()
endmacro()

####################################################################################

set_and_check(GGML_INCLUDE_DIR "${PACKAGE_PREFIX_DIR}/include")
set_and_check(GGML_LIB_DIR "${PACKAGE_PREFIX_DIR}/lib")
#set_and_check(GGML_BIN_DIR "${PACKAGE_PREFIX_DIR}/bin")

find_package(Threads REQUIRED)

find_library(GGML_LIBRARY ggml
    REQUIRED
    HINTS ${GGML_LIB_DIR}
    NO_CMAKE_FIND_ROOT_PATH)

add_library(ggml::ggml UNKNOWN IMPORTED)
set_target_properties(ggml::ggml
    PROPERTIES
        IMPORTED_LOCATION "${GGML_LIBRARY}")

find_library(GGML_BASE_LIBRARY ggml-base
    REQUIRED
    HINTS ${GGML_LIB_DIR}
    NO_CMAKE_FIND_ROOT_PATH)

add_library(ggml::ggml-base UNKNOWN IMPORTED)
set_target_properties(ggml::ggml-base
    PROPERTIES
        IMPORTED_LOCATION "${GGML_BASE_LIBRARY}")

if (NOT GGML_SHARED_LIB)
    if (APPLE AND GGML_ACCELERATE)
        find_library(ACCELERATE_FRAMEWORK Accelerate REQUIRED)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES ${ACCELERATE_FRAMEWORK})
    endif()

    if (GGML_OPENMP)
        find_package(OpenMP REQUIRED)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES OpenMP::OpenMP_C OpenMP::OpenMP_CXX)
    endif()

    if (GGML_CPU_HBM)
        find_library(memkind memkind REQUIRED)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES memkind)
    endif()

    if (GGML_BLAS)
        find_package(BLAS REQUIRED)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES ${BLAS_LIBRARIES})
        list(APPEND GGML_CPU_INTERFACE_LINK_OPTIONS   ${BLAS_LINKER_FLAGS})
    endif()

    if (GGML_CUDA)
        find_package(CUDAToolkit REQUIRED)
    endif()

    if (GGML_METAL)
        find_library(FOUNDATION_LIBRARY Foundation REQUIRED)
        find_library(METAL_FRAMEWORK    Metal REQUIRED)
        find_library(METALKIT_FRAMEWORK MetalKit REQUIRED)

        list(APPEND GGML_METAL_INTERFACE_LINK_LIBRARIES
                    ${FOUNDATION_LIBRARY} ${METAL_FRAMEWORK} ${METALKIT_FRAMEWORK})
    endif()

    if (GGML_VULKAN)
        find_package(Vulkan REQUIRED)
        list(APPEND GGML_VULKAN_INTERFACE_LINK_LIBRARIES Vulkan::Vulkan)
    endif()

    if (GGML_HIP)
        find_package(hip     REQUIRED)
        find_package(hipblas REQUIRED)
        find_package(rocblas REQUIRED)
        list(APPEND GGML_HIP_INTERFACE_LINK_LIBRARIES hip::host roc::rocblas roc::hipblas)
    endif()

    if (GGML_SYCL)
        find_package(DNNL)
        if (${DNNL_FOUND} AND GGML_SYCL_TARGET STREQUAL "INTEL")
            list(APPEND GGML_SYCL_INTERFACE_LINK_LIBRARIES DNNL::dnnl)
        endif()
        if (WIN32)
            find_package(IntelSYCL REQUIRED)
            find_package(MKL       REQUIRED)
            list(APPEND GGML_SYCL_INTERFACE_LINK_LIBRARIES IntelSYCL::SYCL_CXX MKL::MKL MKL::MKL_SYCL)
        endif()
    endif()
endif()

set(_ggml_all_targets "")
foreach(_ggml_backend ${GGML_AVAILABLE_BACKENDS})
    string(REPLACE "-" "_" _ggml_backend_pfx "${_ggml_backend}")
    string(TOUPPER "${_ggml_backend_pfx}" _ggml_backend_pfx)

    find_library(${_ggml_backend_pfx}_LIBRARY ${_ggml_backend}
        REQUIRED
        HINTS ${GGML_LIB_DIR}
        NO_CMAKE_FIND_ROOT_PATH)

    message(STATUS "Found ${${_ggml_backend_pfx}_LIBRARY}")

    add_library(ggml::${_ggml_backend} UNKNOWN IMPORTED)
    set_target_properties(ggml::${_ggml_backend}
        PROPERTIES
            INTERFACE_INCLUDE_DIRECTORIES "${GGML_INCLUDE_DIR}"
            IMPORTED_LINK_INTERFACE_LANGUAGES "CXX"
            IMPORTED_LOCATION "${${_ggml_backend_pfx}_LIBRARY}"
            INTERFACE_COMPILE_FEATURES c_std_90
            POSITION_INDEPENDENT_CODE ON)

    string(REGEX MATCH "^ggml-cpu" is_cpu_variant "${_ggml_backend}")
    if(is_cpu_variant)
        list(APPEND GGML_CPU_INTERFACE_LINK_LIBRARIES "ggml::ggml-base")
        set_target_properties(ggml::${_ggml_backend}
           PROPERTIES
               INTERFACE_LINK_LIBRARIES "${GGML_CPU_INTERFACE_LINK_LIBRARIES}")

        if(GGML_CPU_INTERFACE_LINK_OPTIONS)
            set_target_properties(ggml::${_ggml_backend}
                PROPERTIES
                    INTERFACE_LINK_OPTIONS "${GGML_CPU_INTERFACE_LINK_OPTIONS}")
        endif()

    else()
        list(APPEND ${_ggml_backend_pfx}_INTERFACE_LINK_LIBRARIES "ggml::ggml-base")
        set_target_properties(ggml::${_ggml_backend}
            PROPERTIES
                INTERFACE_LINK_LIBRARIES "${${_ggml_backend_pfx}_INTERFACE_LINK_LIBRARIES}")

        if(${_ggml_backend_pfx}_INTERFACE_LINK_OPTIONS)
            set_target_properties(ggml::${_ggml_backend}
                PROPERTIES
                    INTERFACE_LINK_OPTIONS "${${_ggml_backend_pfx}_INTERFACE_LINK_OPTIONS}")
        endif()
    endif()

    list(APPEND _ggml_all_targets ggml::${_ggml_backend})
endforeach()

list(APPEND GGML_INTERFACE_LINK_LIBRARIES ggml::ggml-base "${_ggml_all_targets}")
set_target_properties(ggml::ggml
    PROPERTIES
        INTERFACE_LINK_LIBRARIES "${GGML_INTERFACE_LINK_LIBRARIES}")

add_library(ggml::all INTERFACE IMPORTED)
set_target_properties(ggml::all
    PROPERTIES
        INTERFACE_LINK_LIBRARIES "${_ggml_all_targets}")

check_required_components(ggml)
