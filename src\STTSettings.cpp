#include "STTSettings.h"
#include <fstream>
#include <iostream>

STTSettingsManager::STTSettingsManager()
{
    InitializePredefinedProfiles();
}

void STTSettingsManager::InitializePredefinedProfiles()
{
    predefinedProfiles_.clear();

    // 1. Ultra Fast - Minimum delay, may have false positives
    {
        STTSettings profile;
        profile.name = "Ultra Fast";
        profile.description = "Fastest response, may have some false positives";
        profile.vadEnergyThreshold = 0.005f;
        profile.vadSilenceFrames = 10; // 0.2 seconds
        profile.vadSpeechFrames = 5;   // 0.1 seconds
        profile.minAudioSeconds = 0.2f;
        profile.silenceThresholdSeconds = 0.15f;
        profile.whisperNoSpeechThreshold = 0.5f;
        profile.modelType = "tiny";
        profile.threads = 2;
        profile.minTextLength = 3;
        profile.enableHallucinationFilter = false;
        predefinedProfiles_.push_back(profile);
    }

    // 2. Fast - Good balance of speed and accuracy
    {
        STTSettings profile;
        profile.name = "Fast";
        profile.description = "Quick response with good accuracy - sub-1 second";
        profile.vadEnergyThreshold = 0.008f;
        profile.vadSilenceFrames = 8;           // 0.16 seconds (reduced from 15)
        profile.vadSpeechFrames = 6;            // Reduced from 8
        profile.minAudioSeconds = 0.2f;         // Reduced from 0.3f
        profile.silenceThresholdSeconds = 0.1f; // Reduced from 0.2f for very fast response
        profile.whisperNoSpeechThreshold = 0.6f;
        profile.modelType = "tiny";
        profile.threads = 4;
        profile.minTextLength = 2; // Reduced from 4 for faster response
        profile.enableHallucinationFilter = true;
        predefinedProfiles_.push_back(profile);
    }

    // 3. Balanced - Default settings (OPTIMIZED FOR 1-SECOND RESPONSE)
    {
        STTSettings profile;
        profile.name = "Balanced";
        profile.description = "Balanced settings for general use - 1 second response";
        profile.vadEnergyThreshold = 0.01f;
        profile.vadSilenceFrames = 10; // 0.2 seconds (reduced from 20)
        profile.vadSpeechFrames = 10;
        profile.minAudioSeconds = 0.3f;          // Reduced from 0.5f
        profile.silenceThresholdSeconds = 0.15f; // Reduced from 0.3f for faster response
        profile.whisperNoSpeechThreshold = 0.7f;
        profile.modelType = "base";
        profile.threads = 4;
        profile.beamSize = 5;
        profile.minTextLength = 3; // Reduced from 5 for faster response
        profile.enableHallucinationFilter = true;
        predefinedProfiles_.push_back(profile);
    }

    // 4. Accurate - Higher quality, slower
    {
        STTSettings profile;
        profile.name = "Accurate";
        profile.description = "High accuracy, slower response";
        profile.vadEnergyThreshold = 0.015f;
        profile.vadSilenceFrames = 25; // 0.5 seconds
        profile.vadSpeechFrames = 15;
        profile.minAudioSeconds = 0.8f;
        profile.silenceThresholdSeconds = 0.4f;
        profile.whisperNoSpeechThreshold = 0.8f;
        profile.whisperEntropyThreshold = 2.8f;
        profile.modelType = "base";
        profile.threads = 6;
        profile.beamSize = 8;
        profile.minTextLength = 6;
        profile.minMeaningfulWords = 2;
        profile.enableHallucinationFilter = true;
        predefinedProfiles_.push_back(profile);
    }

    // 5. Premium - Best quality with small model
    {
        STTSettings profile;
        profile.name = "Premium";
        profile.description = "Best quality with small model";
        profile.vadEnergyThreshold = 0.012f;
        profile.vadSilenceFrames = 30; // 0.6 seconds
        profile.vadSpeechFrames = 12;
        profile.minAudioSeconds = 1.0f;
        profile.silenceThresholdSeconds = 0.5f;
        profile.whisperNoSpeechThreshold = 0.85f;
        profile.whisperEntropyThreshold = 2.6f;
        profile.modelType = "small";
        profile.threads = 8;
        profile.beamSize = 10;
        profile.minTextLength = 8;
        profile.minMeaningfulWords = 2;
        profile.enableHallucinationFilter = true;
        predefinedProfiles_.push_back(profile);
    }

    // 6. Noise Resistant - For noisy environments
    {
        STTSettings profile;
        profile.name = "Noise Resistant";
        profile.description = "Optimized for noisy environments";
        profile.vadEnergyThreshold = 0.02f;
        profile.vadSilenceFrames = 35; // 0.7 seconds
        profile.vadSpeechFrames = 20;  // 0.4 seconds
        profile.vadSmoothingFactor = 0.5f;
        profile.minAudioSeconds = 1.2f;
        profile.silenceThresholdSeconds = 0.6f;
        profile.audioAmplitudeThreshold = 0.02f;
        profile.whisperNoSpeechThreshold = 0.9f;
        profile.whisperEntropyThreshold = 3.0f;
        profile.modelType = "base";
        profile.threads = 4;
        profile.beamSize = 5;
        profile.minTextLength = 10;
        profile.minMeaningfulWords = 3;
        profile.enableHallucinationFilter = true;
        predefinedProfiles_.push_back(profile);
    }

    // 7. Quiet Speaker - For soft/quiet voices
    {
        STTSettings profile;
        profile.name = "Quiet Speaker";
        profile.description = "Optimized for quiet or soft voices";
        profile.vadEnergyThreshold = 0.003f;
        profile.vadSilenceFrames = 15;
        profile.vadSpeechFrames = 8;
        profile.vadSmoothingFactor = 0.2f;
        profile.minAudioSeconds = 0.4f;
        profile.silenceThresholdSeconds = 0.2f;
        profile.audioAmplitudeThreshold = 0.005f;
        profile.whisperNoSpeechThreshold = 0.4f;
        profile.whisperEntropyThreshold = 2.0f;
        profile.modelType = "base";
        profile.threads = 4;
        profile.beamSize = 5;
        profile.minTextLength = 3;
        profile.minMeaningfulWords = 1;
        profile.enableHallucinationFilter = true;
        predefinedProfiles_.push_back(profile);
    }

    // 8. Dictation Mode - For long continuous speech
    {
        STTSettings profile;
        profile.name = "Dictation";
        profile.description = "Optimized for continuous dictation";
        profile.vadEnergyThreshold = 0.008f;
        profile.vadSilenceFrames = 40; // 0.8 seconds
        profile.vadSpeechFrames = 10;
        profile.minAudioSeconds = 2.0f; // Longer segments
        profile.silenceThresholdSeconds = 0.8f;
        profile.whisperNoSpeechThreshold = 0.7f;
        profile.modelType = "base";
        profile.threads = 6;
        profile.beamSize = 8;
        profile.minTextLength = 15;
        profile.minMeaningfulWords = 3;
        profile.enableHallucinationFilter = true;
        predefinedProfiles_.push_back(profile);
    }

    // 9. Gaming/Commands - For short commands
    {
        STTSettings profile;
        profile.name = "Gaming";
        profile.description = "Quick response for gaming commands";
        profile.vadEnergyThreshold = 0.012f;
        profile.vadSilenceFrames = 12; // 0.24 seconds
        profile.vadSpeechFrames = 6;
        profile.minAudioSeconds = 0.25f;
        profile.silenceThresholdSeconds = 0.15f;
        profile.whisperNoSpeechThreshold = 0.6f;
        profile.modelType = "tiny";
        profile.threads = 2;
        profile.beamSize = 3;
        profile.minTextLength = 2;
        profile.minMeaningfulWords = 1;
        profile.enableHallucinationFilter = false;
        predefinedProfiles_.push_back(profile);
    }

    // 10. Maximum Quality - Best possible accuracy
    {
        STTSettings profile;
        profile.name = "Maximum Quality";
        profile.description = "Best possible accuracy, slowest response";
        profile.vadEnergyThreshold = 0.02f;
        profile.vadSilenceFrames = 50; // 1.0 second
        profile.vadSpeechFrames = 25;
        profile.vadSmoothingFactor = 0.6f;
        profile.minAudioSeconds = 1.5f;
        profile.silenceThresholdSeconds = 1.0f;
        profile.audioAmplitudeThreshold = 0.015f;
        profile.whisperNoSpeechThreshold = 0.95f;
        profile.whisperEntropyThreshold = 3.2f;
        profile.whisperLogprobThreshold = -0.5f;
        profile.modelType = "small";
        profile.threads = 8;
        profile.beamSize = 15;
        profile.minTextLength = 12;
        profile.minMeaningfulWords = 4;
        profile.enableHallucinationFilter = true;
        predefinedProfiles_.push_back(profile);
    }
}

std::vector<STTSettings> STTSettingsManager::GetPredefinedProfiles() const
{
    return predefinedProfiles_;
}

STTSettings STTSettingsManager::GetProfile(const std::string &name) const
{
    for (const auto &profile : predefinedProfiles_)
    {
        if (profile.name == name)
        {
            return profile;
        }
    }

    // Return first profile (balanced) if not found
    if (!predefinedProfiles_.empty())
    {
        return predefinedProfiles_[2]; // Balanced
    }

    return STTSettings(); // Default constructor
}

bool STTSettingsManager::SaveSettings(const STTSettings &settings, const std::string &filename) const
{
    std::ofstream file(filename);
    if (!file.is_open())
    {
        return false;
    }

    file << "# STT Settings Configuration\n";
    file << "name=" << settings.name << "\n";
    file << "description=" << settings.description << "\n";
    file << "vadEnergyThreshold=" << settings.vadEnergyThreshold << "\n";
    file << "vadSilenceThreshold=" << settings.vadSilenceThreshold << "\n";
    file << "vadSilenceFrames=" << settings.vadSilenceFrames << "\n";
    file << "vadSpeechFrames=" << settings.vadSpeechFrames << "\n";
    file << "vadSmoothingFactor=" << settings.vadSmoothingFactor << "\n";
    file << "minAudioSeconds=" << settings.minAudioSeconds << "\n";
    file << "silenceThresholdSeconds=" << settings.silenceThresholdSeconds << "\n";
    file << "audioAmplitudeThreshold=" << settings.audioAmplitudeThreshold << "\n";
    file << "whisperNoSpeechThreshold=" << settings.whisperNoSpeechThreshold << "\n";
    file << "whisperEntropyThreshold=" << settings.whisperEntropyThreshold << "\n";
    file << "whisperLogprobThreshold=" << settings.whisperLogprobThreshold << "\n";
    file << "modelType=" << settings.modelType << "\n";
    file << "threads=" << settings.threads << "\n";
    file << "beamSize=" << settings.beamSize << "\n";
    file << "minTextLength=" << settings.minTextLength << "\n";
    file << "minMeaningfulWords=" << settings.minMeaningfulWords << "\n";
    file << "enableHallucinationFilter=" << (settings.enableHallucinationFilter ? "true" : "false") << "\n";

    return true;
}

bool STTSettingsManager::LoadSettings(STTSettings &settings, const std::string &filename) const
{
    std::ifstream file(filename);
    if (!file.is_open())
    {
        return false;
    }

    std::string line;
    while (std::getline(file, line))
    {
        if (line.empty() || line[0] == '#')
            continue;

        size_t pos = line.find('=');
        if (pos == std::string::npos)
            continue;

        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        if (key == "name")
            settings.name = value;
        else if (key == "description")
            settings.description = value;
        else if (key == "vadEnergyThreshold")
            settings.vadEnergyThreshold = std::stof(value);
        else if (key == "vadSilenceThreshold")
            settings.vadSilenceThreshold = std::stof(value);
        else if (key == "vadSilenceFrames")
            settings.vadSilenceFrames = std::stoi(value);
        else if (key == "vadSpeechFrames")
            settings.vadSpeechFrames = std::stoi(value);
        else if (key == "vadSmoothingFactor")
            settings.vadSmoothingFactor = std::stof(value);
        else if (key == "minAudioSeconds")
            settings.minAudioSeconds = std::stof(value);
        else if (key == "silenceThresholdSeconds")
            settings.silenceThresholdSeconds = std::stof(value);
        else if (key == "audioAmplitudeThreshold")
            settings.audioAmplitudeThreshold = std::stof(value);
        else if (key == "whisperNoSpeechThreshold")
            settings.whisperNoSpeechThreshold = std::stof(value);
        else if (key == "whisperEntropyThreshold")
            settings.whisperEntropyThreshold = std::stof(value);
        else if (key == "whisperLogprobThreshold")
            settings.whisperLogprobThreshold = std::stof(value);
        else if (key == "modelType")
            settings.modelType = value;
        else if (key == "threads")
            settings.threads = std::stoi(value);
        else if (key == "beamSize")
            settings.beamSize = std::stoi(value);
        else if (key == "minTextLength")
            settings.minTextLength = std::stoi(value);
        else if (key == "minMeaningfulWords")
            settings.minMeaningfulWords = std::stoi(value);
        else if (key == "enableHallucinationFilter")
            settings.enableHallucinationFilter = (value == "true");
    }

    return true;
}