#include <windows.h>
#include <commctrl.h>
#include <string>
#include <memory>
#include <iostream>
#include <mutex>

#include "AudioCapture.h"
#include "SpeechRecognizer.h"
#include "TranscriptionManager.h"
#include "STTSettings.h"
#include "HighPerformanceAudioProcessor.h"

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "uxtheme.lib")

// Modern color scheme - Dark blue background with bright orange accents
#define COLOR_DARK_BLUE RGB(15, 25, 45)      // Dark blue background
#define COLOR_MEDIUM_BLUE RGB(25, 45, 75)    // Medium blue for panels
#define COLOR_LIGHT_BLUE RGB(45, 75, 115)    // Light blue for hover
#define COLOR_BRIGHT_ORANGE RGB(255, 140, 0) // Bright orange for text/accents
#define COLOR_LIGHT_ORANGE RGB(255, 180, 80) // Light orange for highlights
#define COLOR_WHITE RGB(255, 255, 255)       // White for important text

// Control IDs
#define ID_START_BUTTON 2001
#define ID_TRANSCRIPTION 2002
#define ID_PROFILE_COMBO 2003
#define ID_CLEAR_BUTTON 2004
#define ID_STATUS_TEXT 2005
#define ID_ENERGY_METER 2006
#define ID_TIMER_ENERGY 2007

// Global variables
HWND hMainWindow;
HWND hStartButton;
HWND hClearButton;
HWND hTranscriptionEdit;
HWND hProfileCombo;
HWND hStatusText;
HWND hEnergyMeter;

// Modern UI components
std::unique_ptr<AudioCapture> audioCapture;
std::unique_ptr<SpeechRecognizer> recognizer;
std::unique_ptr<TranscriptionManager> transcriptionManager;
std::unique_ptr<HighPerformanceAudioProcessor> audioProcessor;
std::unique_ptr<STTSettingsManager> settingsManager;
STTSettings currentSettings;

// UI state
bool isRecording = false;
std::string g_pendingTranscription;
std::mutex g_transcriptionMutex;

// Debug logging
FILE *g_logFile = nullptr;

// Modern UI fonts
HFONT hFontTitle;
HFONT hFontButton;
HFONT hFontText;

// Custom window procedure
LRESULT CALLBACK ModernWindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);

// UI creation functions
void CreateModernControls(HWND hwnd);
void InitializeAudioComponents();
void StartRecording();
void StopRecording();
void UpdateTranscription(const std::string &text);
void OnProfileChanged();

// Custom drawing
void DrawModernButton(HDC hdc, RECT rect, const std::string &text, bool pressed);
void DrawEnergyMeter(HDC hdc, RECT rect, float energy);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // *** DEBUG: Create log file for debugging ***
    fopen_s(&g_logFile, "UltraFlexSTT_debug.log", "w");
    if (g_logFile)
    {
        fprintf(g_logFile, "=== UltraFlexSTT DEBUG LOG STARTED ===\n");
        fprintf(g_logFile, "Initializing application...\n");
        fflush(g_logFile);
    }

    // *** DEBUG: Also try to allocate console for debugging ***
    AllocConsole();

    // Redirect stdout, stdin, stderr to console
    FILE *pCout;
    FILE *pCin;
    FILE *pCerr;
    freopen_s(&pCout, "CONOUT$", "w", stdout);
    freopen_s(&pCin, "CONIN$", "r", stdin);
    freopen_s(&pCerr, "CONOUT$", "w", stderr);

    // Make cout, wcout, cin, wcin, wcerr, cerr, wclog and clog
    // point to console as well
    std::ios::sync_with_stdio(true);

    // Set console title
    SetConsoleTitle("UltraFlexSTT Debug Console");

    std::cout << "=== UltraFlexSTT DEBUG CONSOLE STARTED ===" << std::endl;
    std::cout << "Initializing application..." << std::endl;

    if (g_logFile)
    {
        fprintf(g_logFile, "Console allocated successfully\n");
        fflush(g_logFile);
    }

    // Initialize common controls
    if (g_logFile)
    {
        fprintf(g_logFile, "Initializing common controls...\n");
        fflush(g_logFile);
    }
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES;
    InitCommonControlsEx(&icex);

    // Register window class
    if (g_logFile)
    {
        fprintf(g_logFile, "Registering window class...\n");
        fflush(g_logFile);
    }
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = ModernWindowProc;
    wc.hInstance = hInstance;
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hbrBackground = CreateSolidBrush(COLOR_DARK_BLUE);
    wc.lpszClassName = "ModernSTTWindow";

    if (!RegisterClassEx(&wc))
    {
        if (g_logFile)
        {
            fprintf(g_logFile, "ERROR: Window registration failed!\n");
            fflush(g_logFile);
            fclose(g_logFile);
        }
        MessageBox(NULL, "Window registration failed!", "Error", MB_ICONEXCLAMATION | MB_OK);
        return 0;
    }

    if (g_logFile)
    {
        fprintf(g_logFile, "Window class registered successfully\n");
        fflush(g_logFile);
    }

    // Create fonts
    hFontTitle = CreateFont(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                            OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                            DEFAULT_PITCH | FF_SWISS, "Segoe UI");

    hFontButton = CreateFont(14, 0, 0, 0, FW_SEMIBOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                             OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                             DEFAULT_PITCH | FF_SWISS, "Segoe UI");

    hFontText = CreateFont(12, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                           OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                           DEFAULT_PITCH | FF_SWISS, "Segoe UI");

    // Create main window
    hMainWindow = CreateWindowEx(
        WS_EX_LAYERED,
        "ModernSTTWindow",
        "UltraFlex STT - High Performance C++ Edition",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 900, 600,
        NULL, NULL, hInstance, NULL);

    if (!hMainWindow)
    {
        MessageBox(NULL, "Window creation failed!", "Error", MB_ICONEXCLAMATION | MB_OK);
        return 0;
    }

    // Set window transparency and show
    SetLayeredWindowAttributes(hMainWindow, 0, 255, LWA_ALPHA);
    ShowWindow(hMainWindow, nCmdShow);
    UpdateWindow(hMainWindow);

    // Initialize components
    if (g_logFile)
    {
        fprintf(g_logFile, "About to initialize audio components...\n");
        fflush(g_logFile);
    }
    InitializeAudioComponents();

    if (g_logFile)
    {
        fprintf(g_logFile, "Audio components initialized, starting message loop...\n");
        fflush(g_logFile);
    }

    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    if (g_logFile)
    {
        fprintf(g_logFile, "Message loop ended, exiting application\n");
        fflush(g_logFile);
        fclose(g_logFile);
    }

    return msg.wParam;
}

LRESULT CALLBACK ModernWindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_CREATE:
        CreateModernControls(hwnd);
        break;

    case WM_PAINT:
    {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hwnd, &ps);

        // Fill background with dark blue
        RECT rect;
        GetClientRect(hwnd, &rect);
        FillRect(hdc, &rect, CreateSolidBrush(COLOR_DARK_BLUE));

        // Draw title
        SetBkMode(hdc, TRANSPARENT);
        SetTextColor(hdc, COLOR_BRIGHT_ORANGE);
        SelectObject(hdc, hFontTitle);

        RECT titleRect = {20, 20, rect.right - 20, 60};
        DrawText(hdc, "UltraFlex STT - C++ High Performance Edition", -1, &titleRect, DT_LEFT | DT_VCENTER);

        // Draw energy meter if recording
        if (isRecording && audioProcessor)
        {
            RECT energyRect = {rect.right - 150, 25, rect.right - 20, 55};
            DrawEnergyMeter(hdc, energyRect, audioProcessor->GetCurrentEnergy());
        }

        EndPaint(hwnd, &ps);
        break;
    }

    case WM_COMMAND:
    {
        switch (LOWORD(wParam))
        {
        case ID_START_BUTTON:
            if (!isRecording)
            {
                StartRecording();
            }
            else
            {
                StopRecording();
            }
            break;

        case ID_CLEAR_BUTTON:
            SetWindowText(hTranscriptionEdit, "");
            break;

        case ID_PROFILE_COMBO:
            if (HIWORD(wParam) == CBN_SELCHANGE)
            {
                OnProfileChanged();
            }
            break;
        }
        break;
    }

    case WM_USER + 1:
    {
        // Handle transcription update
        std::string textToAdd;
        {
            std::lock_guard<std::mutex> lock(g_transcriptionMutex);
            textToAdd = g_pendingTranscription;
        }

        if (!textToAdd.empty())
        {
            int length = GetWindowTextLength(hTranscriptionEdit);
            std::string displayText = textToAdd + "\r\n";
            SendMessage(hTranscriptionEdit, EM_SETSEL, length, length);
            SendMessage(hTranscriptionEdit, EM_REPLACESEL, 0, (LPARAM)displayText.c_str());
            SendMessage(hTranscriptionEdit, EM_SCROLL, SB_BOTTOM, 0);
        }
        break;
    }

    case WM_CTLCOLORSTATIC:
    case WM_CTLCOLOREDIT:
    {
        HDC hdc = (HDC)wParam;
        SetTextColor(hdc, COLOR_BRIGHT_ORANGE);
        SetBkColor(hdc, COLOR_MEDIUM_BLUE);
        return (LRESULT)CreateSolidBrush(COLOR_MEDIUM_BLUE);
    }

    case WM_CTLCOLORBTN:
    {
        HDC hdc = (HDC)wParam;
        HWND hButton = (HWND)lParam;

        // Set button colors - bright orange text on light blue background
        SetTextColor(hdc, COLOR_WHITE);
        SetBkColor(hdc, COLOR_LIGHT_BLUE);
        return (LRESULT)CreateSolidBrush(COLOR_LIGHT_BLUE);
    }

    case WM_TIMER:
    {
        if (wParam == ID_TIMER_ENERGY && isRecording)
        {
            // Invalidate only the energy meter area to trigger repaint
            RECT rect;
            GetClientRect(hwnd, &rect);
            RECT energyRect = {rect.right - 150, 25, rect.right - 20, 75};
            InvalidateRect(hwnd, &energyRect, FALSE);
        }
        break;
    }

    case WM_DESTROY:
        KillTimer(hwnd, ID_TIMER_ENERGY);
        if (audioProcessor)
            audioProcessor->Stop();
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
    return 0;
}

void CreateModernControls(HWND hwnd)
{
    RECT rect;
    GetClientRect(hwnd, &rect);

    // Profile selection
    CreateWindow("STATIC", "Performance Profile:", WS_VISIBLE | WS_CHILD,
                 20, 80, 150, 25, hwnd, NULL, NULL, NULL);

    hProfileCombo = CreateWindowEx(WS_EX_CLIENTEDGE, "COMBOBOX", "",
                                   WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
                                   180, 78, 200, 150, hwnd, (HMENU)ID_PROFILE_COMBO, NULL, NULL);

    // Populate profile dropdown with all 10 predefined profiles
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Ultra Fast");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Fast");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Balanced");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Accurate");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Premium");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Noise Resistant");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Quiet Speaker");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Dictation");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Gaming");
    SendMessage(hProfileCombo, CB_ADDSTRING, 0, (LPARAM) "Maximum Quality");
    SendMessage(hProfileCombo, CB_SETCURSEL, 2, 0); // Default to "Balanced"

    // Start/Stop button with custom colors
    hStartButton = CreateWindow("BUTTON", "START RECORDING",
                                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                400, 75, 150, 35, hwnd, (HMENU)ID_START_BUTTON, NULL, NULL);

    // Clear button with custom colors
    hClearButton = CreateWindow("BUTTON", "CLEAR",
                                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                570, 75, 80, 35, hwnd, (HMENU)ID_CLEAR_BUTTON, NULL, NULL);

    // Transcription area
    CreateWindow("STATIC", "Live Transcription:", WS_VISIBLE | WS_CHILD,
                 20, 130, 150, 25, hwnd, NULL, NULL, NULL);

    hTranscriptionEdit = CreateWindowEx(WS_EX_CLIENTEDGE, "EDIT",
                                        "Click START RECORDING and speak into your microphone...",
                                        WS_VISIBLE | WS_CHILD | ES_MULTILINE | ES_AUTOVSCROLL | WS_VSCROLL,
                                        20, 160, rect.right - 40, rect.bottom - 220,
                                        hwnd, (HMENU)ID_TRANSCRIPTION, NULL, NULL);

    // Status text
    hStatusText = CreateWindow("STATIC", "Ready - Select a profile and click START",
                               WS_VISIBLE | WS_CHILD,
                               20, rect.bottom - 50, rect.right - 200, 25,
                               hwnd, (HMENU)ID_STATUS_TEXT, NULL, NULL);

    // Set fonts
    SendMessage(hProfileCombo, WM_SETFONT, (WPARAM)hFontText, TRUE);
    SendMessage(hStartButton, WM_SETFONT, (WPARAM)hFontButton, TRUE);
    SendMessage(hTranscriptionEdit, WM_SETFONT, (WPARAM)hFontText, TRUE);
    SendMessage(hStatusText, WM_SETFONT, (WPARAM)hFontText, TRUE);
}

void InitializeAudioComponents()
{
    try
    {
        if (g_logFile)
        {
            fprintf(g_logFile, "=== INITIALIZING AUDIO COMPONENTS ===\n");
            fflush(g_logFile);
        }
        std::cout << "=== INITIALIZING AUDIO COMPONENTS ===" << std::endl;

        // Initialize settings manager and load default profile
        if (g_logFile)
        {
            fprintf(g_logFile, "1. Creating STTSettingsManager...\n");
            fflush(g_logFile);
        }
        std::cout << "1. Creating STTSettingsManager..." << std::endl;
        settingsManager = std::make_unique<STTSettingsManager>();

        if (g_logFile)
        {
            fprintf(g_logFile, "2. Loading 'Balanced' profile...\n");
            fflush(g_logFile);
        }
        std::cout << "2. Loading 'Balanced' profile..." << std::endl;
        currentSettings = settingsManager->GetProfile("Balanced");

        if (g_logFile)
        {
            fprintf(g_logFile, "   Profile loaded - minAudio: %fs, silence: %fs\n",
                    currentSettings.minAudioSeconds, currentSettings.silenceThresholdSeconds);
            fflush(g_logFile);
        }
        std::cout << "   Profile loaded - minAudio: " << currentSettings.minAudioSeconds
                  << "s, silence: " << currentSettings.silenceThresholdSeconds << "s" << std::endl;

        // Initialize recognizer with current settings
        if (g_logFile)
        {
            fprintf(g_logFile, "3. Creating SpeechRecognizer...\n");
            fflush(g_logFile);
        }
        std::cout << "3. Creating SpeechRecognizer..." << std::endl;
        recognizer = std::make_unique<SpeechRecognizer>();

        if (g_logFile)
        {
            fprintf(g_logFile, "4. Configuring SpeechRecognizer with TINY model...\n");
            fflush(g_logFile);
        }
        SpeechRecognizer::Config recognizerConfig;
        recognizerConfig.model = SpeechRecognizer::Model::TINY;
        recognizerConfig.threads = 4;

        if (g_logFile)
        {
            fprintf(g_logFile, "5. Initializing SpeechRecognizer...\n");
            fflush(g_logFile);
        }
        std::cout << "4. Initializing SpeechRecognizer with TINY model..." << std::endl;
        bool recognizerInit = recognizer->Initialize(recognizerConfig);

        if (g_logFile)
        {
            fprintf(g_logFile, "   SpeechRecognizer initialization: %s\n", (recognizerInit ? "SUCCESS" : "FAILED"));
            fflush(g_logFile);
        }
        std::cout << "   SpeechRecognizer initialization: " << (recognizerInit ? "SUCCESS" : "FAILED") << std::endl;

        if (!recognizerInit)
        {
            if (g_logFile)
            {
                fprintf(g_logFile, "ERROR: SpeechRecognizer initialization failed!\n");
                fflush(g_logFile);
            }
            throw std::runtime_error("Failed to initialize SpeechRecognizer");
        }

        // Initialize transcription manager
        if (g_logFile)
        {
            fprintf(g_logFile, "6. Creating TranscriptionManager...\n");
            fflush(g_logFile);
        }
        std::cout << "5. Creating TranscriptionManager..." << std::endl;
        transcriptionManager = std::make_unique<TranscriptionManager>();

        // Initialize high-performance audio processor
        if (g_logFile)
        {
            fprintf(g_logFile, "7. Creating HighPerformanceAudioProcessor...\n");
            fflush(g_logFile);
        }
        std::cout << "6. Creating HighPerformanceAudioProcessor..." << std::endl;
        audioProcessor = std::make_unique<HighPerformanceAudioProcessor>();

        // Configure audio processor with settings-based config (FIXED FOR LOW MICROPHONE GAIN)
        if (g_logFile)
        {
            fprintf(g_logFile, "8. Configuring audio processor...\n");
            fflush(g_logFile);
        }
        std::cout << "7. Configuring audio processor..." << std::endl;
        HighPerformanceAudioProcessor::Config audioConfig;
        audioConfig.sampleRate = 16000;
        audioConfig.frameSize = 160;                                             // 10ms frames
        audioConfig.preRecordingDuration = 0.2f;                                 // Match Python RealtimeSTT
        audioConfig.postSpeechSilence = currentSettings.silenceThresholdSeconds; // Use actual settings
        audioConfig.minRecordingDuration = currentSettings.minAudioSeconds;      // Use actual settings
        audioConfig.vadMode = WebRTCVAD::Mode::VERY_AGGRESSIVE;                  // More sensitive for low-gain mics
        audioConfig.enableAudioNormalization = true;
        audioConfig.enableNoiseReduction = false; // Disable to preserve low signals

        std::cout << "   Audio config: " << audioConfig.sampleRate << "Hz, "
                  << audioConfig.frameSize << " frame size, VAD: VERY_AGGRESSIVE" << std::endl;
        std::cout << "   Min recording duration: " << audioConfig.minRecordingDuration << "s" << std::endl;
        std::cout << "   Post speech silence: " << audioConfig.postSpeechSilence << "s" << std::endl;

        if (g_logFile)
        {
            fprintf(g_logFile, "9. Initializing audio processor...\n");
            fflush(g_logFile);
        }
        std::cout << "8. Initializing audio processor..." << std::endl;
        bool audioProcessorInit = audioProcessor->Initialize(audioConfig);

        if (g_logFile)
        {
            fprintf(g_logFile, "   AudioProcessor initialization: %s\n", (audioProcessorInit ? "SUCCESS" : "FAILED"));
            fflush(g_logFile);
        }
        std::cout << "   AudioProcessor initialization: " << (audioProcessorInit ? "SUCCESS" : "FAILED") << std::endl;

        if (!audioProcessorInit)
        {
            if (g_logFile)
            {
                fprintf(g_logFile, "ERROR: AudioProcessor initialization failed!\n");
                fflush(g_logFile);
            }
            throw std::runtime_error("Failed to initialize audio processor");
        }

        // Set up callback to receive transcriptions (do this ONCE)
        if (g_logFile)
        {
            fprintf(g_logFile, "9. Setting up transcription callback...\n");
            fflush(g_logFile);
        }
        std::cout << "9. Setting up transcription callback..." << std::endl;

        try {
            recognizer->SetCallback([](const std::string &text, bool isFinal)
                                    {
                if (isFinal && !text.empty()) {
                    std::cout << "*** TRANSCRIPTION RECEIVED: '" << text << "' ***" << std::endl;
                    {
                        std::lock_guard<std::mutex> lock(g_transcriptionMutex);
                        g_pendingTranscription = text;
                    }
                    PostMessage(hMainWindow, WM_USER + 1, 0, 0);
                } });

            if (g_logFile)
            {
                fprintf(g_logFile, "   Transcription callback set successfully\n");
                fflush(g_logFile);
            }
            std::cout << "   Transcription callback set successfully" << std::endl;
        } catch (const std::exception& e) {
            if (g_logFile)
            {
                fprintf(g_logFile, "   ERROR setting transcription callback: %s\n", e.what());
                fflush(g_logFile);
            }
            std::cerr << "   ERROR setting transcription callback: " << e.what() << std::endl;
            throw;
        }

        // Set audio processing callback
        if (g_logFile)
        {
            fprintf(g_logFile, "10. Setting up audio processing callback...\n");
            fflush(g_logFile);
        }
        std::cout << "10. Setting up audio processing callback..." << std::endl;

        try {
            audioProcessor->SetCallback([](const std::vector<float> &audioData, bool isEndOfSpeech)
                                        {
                if (isEndOfSpeech && recognizer) {
                    try {
                        std::cout << ">>> PROCESSING AUDIO CHUNK: " << audioData.size() << " samples <<<" << std::endl;
                        // Process the audio
                        recognizer->ProcessAudio(audioData);
                    } catch (const std::exception& e) {
                        std::cerr << "!!! TRANSCRIPTION ERROR: " << e.what() << std::endl;
                    }
                } });

            if (g_logFile)
            {
                fprintf(g_logFile, "   Audio processing callback set successfully\n");
                fflush(g_logFile);
            }
            std::cout << "   Audio processing callback set successfully" << std::endl;
        } catch (const std::exception& e) {
            if (g_logFile)
            {
                fprintf(g_logFile, "   ERROR setting audio processing callback: %s\n", e.what());
                fflush(g_logFile);
            }
            std::cerr << "   ERROR setting audio processing callback: " << e.what() << std::endl;
            throw;
        }

        // Initialize audio capture
        if (g_logFile)
        {
            fprintf(g_logFile, "11. Creating AudioCapture...\n");
            fflush(g_logFile);
        }
        std::cout << "11. Creating AudioCapture..." << std::endl;
        audioCapture = std::make_unique<AudioCapture>();

        if (g_logFile)
        {
            fprintf(g_logFile, "12. Initializing AudioCapture (16kHz, 512 buffer)...\n");
            fflush(g_logFile);
        }
        std::cout << "12. Initializing AudioCapture (16kHz, 512 buffer)..." << std::endl;
        bool audioCaptureInit = audioCapture->Initialize(16000, 512);

        if (g_logFile)
        {
            fprintf(g_logFile, "   AudioCapture initialization: %s\n", (audioCaptureInit ? "SUCCESS" : "FAILED"));
            fflush(g_logFile);
        }
        std::cout << "   AudioCapture initialization: " << (audioCaptureInit ? "SUCCESS" : "FAILED") << std::endl;

        if (!audioCaptureInit)
        {
            if (g_logFile)
            {
                fprintf(g_logFile, "ERROR: AudioCapture initialization failed!\n");
                fflush(g_logFile);
            }
            throw std::runtime_error("Failed to initialize audio capture");
        }

        if (g_logFile)
        {
            fprintf(g_logFile, "=== ALL AUDIO COMPONENTS INITIALIZED SUCCESSFULLY ===\n");
            fflush(g_logFile);
        }
        std::cout << "=== ALL AUDIO COMPONENTS INITIALIZED SUCCESSFULLY ===" << std::endl;
    }
    catch (const std::exception &e)
    {
        if (g_logFile)
        {
            fprintf(g_logFile, "ERROR: Failed to initialize audio components: %s\n", e.what());
            fflush(g_logFile);
        }
        std::cerr << "Failed to initialize audio components: " << e.what() << std::endl;
        MessageBox(hMainWindow, "Failed to initialize audio components", "Error", MB_OK | MB_ICONERROR);
    }
}

void StartRecording()
{
    try
    {
        if (!audioCapture || !audioProcessor)
        {
            MessageBox(hMainWindow, "Audio components not initialized", "Error", MB_OK | MB_ICONERROR);
            return;
        }

        // Start audio processor
        audioProcessor->Start();

        // Start audio capture with callback to audio processor
        audioCapture->StartCapture([](const std::vector<float> &audioData)
                                   {
            if (audioProcessor) {
                audioProcessor->ProcessAudioFrame(audioData);
            } });

        isRecording = true;
        SetWindowText(hStartButton, "STOP RECORDING");
        SetWindowText(hStatusText, "Recording... Speak into your microphone");

        // Start energy meter update timer (50ms = 20 FPS)
        SetTimer(hMainWindow, ID_TIMER_ENERGY, 50, NULL);

        // Force window repaint to show energy meter
        InvalidateRect(hMainWindow, NULL, TRUE);

        std::cout << "Recording started with current profile" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "Failed to start recording: " << e.what() << std::endl;
        MessageBox(hMainWindow, "Failed to start recording", "Error", MB_OK | MB_ICONERROR);
    }
}

void StopRecording()
{
    try
    {
        if (audioCapture)
        {
            audioCapture->StopCapture();
        }

        if (audioProcessor)
        {
            audioProcessor->Stop();
        }

        isRecording = false;
        SetWindowText(hStartButton, "START RECORDING");
        SetWindowText(hStatusText, "Recording stopped. Click START to record again.");

        // Stop energy meter update timer
        KillTimer(hMainWindow, ID_TIMER_ENERGY);

        // Force window repaint to hide energy meter
        InvalidateRect(hMainWindow, NULL, TRUE);

        std::cout << "Recording stopped" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "Error stopping recording: " << e.what() << std::endl;
    }
}

void OnProfileChanged()
{
    if (g_logFile)
    {
        fprintf(g_logFile, "=== PROFILE CHANGE REQUESTED ===\n");
        fflush(g_logFile);
    }
    std::cout << "=== PROFILE CHANGE REQUESTED ===" << std::endl;

    int selectedIndex = SendMessage(hProfileCombo, CB_GETCURSEL, 0, 0);
    if (g_logFile)
    {
        fprintf(g_logFile, "Selected index: %d\n", selectedIndex);
        fflush(g_logFile);
    }
    std::cout << "Selected index: " << selectedIndex << std::endl;

    if (selectedIndex == CB_ERR)
    {
        if (g_logFile)
        {
            fprintf(g_logFile, "ERROR: No valid selection, returning\n");
            fflush(g_logFile);
        }
        std::cout << "ERROR: No valid selection, returning" << std::endl;
        return;
    }

    std::vector<std::string> profileNames = {
        "Ultra Fast", "Fast", "Balanced", "Accurate", "Premium",
        "Noise Resistant", "Quiet Speaker", "Dictation", "Gaming", "Maximum Quality"};

    if (selectedIndex >= 0 && selectedIndex < profileNames.size())
    {
        std::cout << "Changing to profile: " << profileNames[selectedIndex] << std::endl;

        try
        {
            // Load new settings
            std::cout << "1. Loading new settings..." << std::endl;
            currentSettings = settingsManager->GetProfile(profileNames[selectedIndex]);
            std::cout << "   Settings loaded successfully" << std::endl;

            // Update audio processor with new settings
            std::cout << "2. Updating audio processor..." << std::endl;
            if (audioProcessor)
            {
                // Stop if recording
                bool wasRecording = isRecording;
                std::cout << "   Was recording: " << (wasRecording ? "YES" : "NO") << std::endl;
                if (wasRecording)
                {
                    std::cout << "   Stopping audio processor..." << std::endl;
                    audioProcessor->Stop();
                }

                // Reconfigure with new settings
                std::cout << "   Creating new audio config..." << std::endl;
                HighPerformanceAudioProcessor::Config config;
                config.sampleRate = 16000;
                config.frameSize = 160;
                config.preRecordingDuration = 1.0f;
                config.postSpeechSilence = currentSettings.silenceThresholdSeconds;
                config.minRecordingDuration = currentSettings.minAudioSeconds;

                // Map profile to VAD mode
                std::cout << "   Mapping VAD mode for profile..." << std::endl;
                if (profileNames[selectedIndex] == "Noise Resistant" || profileNames[selectedIndex] == "Quiet Speaker")
                {
                    config.vadMode = WebRTCVAD::Mode::VERY_AGGRESSIVE;
                    std::cout << "   VAD Mode: VERY_AGGRESSIVE" << std::endl;
                }
                else if (profileNames[selectedIndex] == "Gaming" || profileNames[selectedIndex] == "Ultra Fast")
                {
                    config.vadMode = WebRTCVAD::Mode::LOW_BITRATE;
                    std::cout << "   VAD Mode: LOW_BITRATE" << std::endl;
                }
                else
                {
                    config.vadMode = WebRTCVAD::Mode::AGGRESSIVE;
                    std::cout << "   VAD Mode: AGGRESSIVE" << std::endl;
                }

                config.enableAudioNormalization = true;
                config.enableNoiseReduction = true;

                if (g_logFile)
                {
                    fprintf(g_logFile, "   Reinitializing audio processor...\n");
                    fflush(g_logFile);
                }
                std::cout << "   Reinitializing audio processor..." << std::endl;
                bool audioReinit = audioProcessor->Initialize(config);
                if (g_logFile)
                {
                    fprintf(g_logFile, "   Audio processor reinitialization: %s\n", (audioReinit ? "SUCCESS" : "FAILED"));
                    fflush(g_logFile);
                }
                std::cout << "   Audio processor reinitialization: " << (audioReinit ? "SUCCESS" : "FAILED") << std::endl;

                // Restart if was recording
                if (wasRecording)
                {
                    std::cout << "   Restarting audio processor..." << std::endl;
                    audioProcessor->Start();
                }
            }
            else
            {
                std::cout << "   WARNING: audioProcessor is null!" << std::endl;
            }

            // Update recognizer if needed
            if (g_logFile)
            {
                fprintf(g_logFile, "3. Updating recognizer...\n");
                fflush(g_logFile);
            }
            std::cout << "3. Updating recognizer..." << std::endl;
            if (recognizer)
            {
                try
                {
                    if (g_logFile)
                    {
                        fprintf(g_logFile, "   Creating recognizer config (TINY model - keeping same model)...\n");
                        fflush(g_logFile);
                    }
                    std::cout << "   Creating recognizer config (TINY model - keeping same model)..." << std::endl;

                    // Use TINY model consistently (same as initialization)
                    SpeechRecognizer::Config config;
                    config.model = SpeechRecognizer::Model::TINY; // Keep same model as initialization
                    config.threads = 4;

                    if (g_logFile)
                    {
                        fprintf(g_logFile, "   Reinitializing recognizer with TINY model...\n");
                        fflush(g_logFile);
                    }
                    std::cout << "   Reinitializing recognizer with TINY model..." << std::endl;
                    bool recognizerReinit = recognizer->Initialize(config);

                    if (g_logFile)
                    {
                        fprintf(g_logFile, "   Recognizer reinitialization: %s\n", (recognizerReinit ? "SUCCESS" : "FAILED"));
                        fflush(g_logFile);
                    }
                    std::cout << "   Recognizer reinitialization: " << (recognizerReinit ? "SUCCESS" : "FAILED") << std::endl;
                }
                catch (const std::exception &e)
                {
                    if (g_logFile)
                    {
                        fprintf(g_logFile, "   ERROR in recognizer reinitialization: %s\n", e.what());
                        fflush(g_logFile);
                    }
                    std::cout << "   ERROR in recognizer reinitialization: " << e.what() << std::endl;
                }
            }
            else
            {
                if (g_logFile)
                {
                    fprintf(g_logFile, "   WARNING: recognizer is null!\n");
                    fflush(g_logFile);
                }
                std::cout << "   WARNING: recognizer is null!" << std::endl;
            }

            if (g_logFile)
            {
                fprintf(g_logFile, "4. Updating UI status...\n");
                fflush(g_logFile);
            }
            std::cout << "4. Updating UI status..." << std::endl;
            std::string statusText = "Profile changed to: " + profileNames[selectedIndex];
            SetWindowText(hStatusText, statusText.c_str());

            if (g_logFile)
            {
                fprintf(g_logFile, "=== PROFILE CHANGE COMPLETED: %s ===\n", profileNames[selectedIndex].c_str());
                fflush(g_logFile);
            }
            std::cout << "=== PROFILE CHANGE COMPLETED: " << profileNames[selectedIndex] << " ===" << std::endl;
        }
        catch (const std::exception &e)
        {
            if (g_logFile)
            {
                fprintf(g_logFile, "!!! PROFILE CHANGE ERROR: %s\n", e.what());
                fflush(g_logFile);
            }
            std::cerr << "!!! PROFILE CHANGE ERROR: " << e.what() << std::endl;
            MessageBox(hMainWindow, "Error changing profile", "Error", MB_OK | MB_ICONWARNING);
        }
    }
    else
    {
        std::cout << "ERROR: Invalid profile index: " << selectedIndex << std::endl;
    }
}

void DrawModernButton(HDC hdc, RECT rect, const std::string &text, bool pressed)
{
    // Draw button background
    HBRUSH bgBrush = CreateSolidBrush(pressed ? COLOR_LIGHT_BLUE : COLOR_MEDIUM_BLUE);
    FillRect(hdc, &rect, bgBrush);
    DeleteObject(bgBrush);

    // Draw button border
    HPEN borderPen = CreatePen(PS_SOLID, 2, COLOR_BRIGHT_ORANGE);
    HPEN oldPen = (HPEN)SelectObject(hdc, borderPen);

    MoveToEx(hdc, rect.left, rect.top, NULL);
    LineTo(hdc, rect.right, rect.top);
    LineTo(hdc, rect.right, rect.bottom);
    LineTo(hdc, rect.left, rect.bottom);
    LineTo(hdc, rect.left, rect.top);

    SelectObject(hdc, oldPen);
    DeleteObject(borderPen);

    // Draw button text
    SetBkMode(hdc, TRANSPARENT);
    SetTextColor(hdc, COLOR_BRIGHT_ORANGE);
    SelectObject(hdc, hFontButton);

    DrawText(hdc, text.c_str(), -1, &rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
}

void DrawEnergyMeter(HDC hdc, RECT rect, float energy)
{
    // Draw background
    HBRUSH bgBrush = CreateSolidBrush(COLOR_MEDIUM_BLUE);
    FillRect(hdc, &rect, bgBrush);
    DeleteObject(bgBrush);

    // Draw border
    HPEN borderPen = CreatePen(PS_SOLID, 1, COLOR_BRIGHT_ORANGE);
    HPEN oldPen = (HPEN)SelectObject(hdc, borderPen);
    Rectangle(hdc, rect.left, rect.top, rect.right, rect.bottom);
    SelectObject(hdc, oldPen);
    DeleteObject(borderPen);

    // Draw energy bar
    int barWidth = (int)((rect.right - rect.left - 4) * (std::min)(1.0f, energy * 10.0f));
    if (barWidth > 0)
    {
        RECT energyRect = {rect.left + 2, rect.top + 2, rect.left + 2 + barWidth, rect.bottom - 2};
        HBRUSH energyBrush = CreateSolidBrush(energy > 0.05f ? COLOR_BRIGHT_ORANGE : COLOR_LIGHT_ORANGE);
        FillRect(hdc, &energyRect, energyBrush);
        DeleteObject(energyBrush);
    }

    // Draw label
    SetBkMode(hdc, TRANSPARENT);
    SetTextColor(hdc, COLOR_WHITE);
    SelectObject(hdc, hFontText);

    RECT labelRect = {rect.left, rect.bottom + 2, rect.right, rect.bottom + 20};
    DrawText(hdc, "Audio Level", -1, &labelRect, DT_CENTER);
}