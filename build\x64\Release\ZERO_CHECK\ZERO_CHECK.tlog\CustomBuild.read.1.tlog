^C:\AI\ULTRAFLEXSTT_CPP\BUILD\CMAKEFILES\6A3B9035D182910FF64FB35FCF7EA3CF\GENERATE.STAMP.RULE
C:\AI\ULTRAFLEXSTT_CPP\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\BUILD\CMAKEFILES\3.31.0-RC2\CMAKECCOMPILER.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\BUILD\CMAKEFILES\3.31.0-RC2\CMAKECXXCOMPILER.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\BUILD\CMAKEFILES\3.31.0-RC2\CMAKERCCOMPILER.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\BUILD\CMAKEFILES\3.31.0-RC2\CMAKESYSTEM.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\GOOGLEMOCK\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\GOOGLEMOCK\CMAKE\GMOCK.PC.IN
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\GOOGLEMOCK\CMAKE\GMOCK_MAIN.PC.IN
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\GOOGLETEST\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\GOOGLETEST\CMAKE\CONFIG.CMAKE.IN
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\GOOGLETEST\CMAKE\GTEST.PC.IN
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\GOOGLETEST\CMAKE\GTEST_MAIN.PC.IN
C:\AI\ULTRAFLEXSTT_CPP\BUILD\_DEPS\GOOGLETEST-SRC\GOOGLETEST\CMAKE\INTERNAL_UTILS.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\PORTAUDIO\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\PORTAUDIO\CMAKE\PORTAUDIOCONFIG.CMAKE.IN
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\PORTAUDIO\CMAKE\CMAKE_UNINSTALL.CMAKE.IN
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\PORTAUDIO\CMAKE\MODULES\FINDJACK.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\PORTAUDIO\CMAKE\MODULES\FINDREGEX.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\PORTAUDIO\CMAKE\PORTAUDIO-2.0.PC.IN
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\CMAKE\BUILD-INFO.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\CMAKE\WHISPER-CONFIG.CMAKE.IN
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\CMAKE\WHISPER.PC.IN
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\GGML\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\GGML\CMAKE\COMMON.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\GGML\CMAKE\GGML-CONFIG.CMAKE.IN
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\GGML\SRC\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\GGML\SRC\GGML-CPU\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\GGML\SRC\GGML-CPU\CMAKE\FINDSIMD.CMAKE
C:\AI\ULTRAFLEXSTT_CPP\EXTERNAL\WHISPER.CPP\SRC\CMAKELISTS.TXT
C:\AI\ULTRAFLEXSTT_CPP\TESTS\CMAKELISTS.TXT
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\BASICCONFIGVERSION-ANYNEWERVERSION.CMAKE.IN
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\BASICCONFIGVERSION-SAMEMAJORVERSION.CMAKE.IN
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKECINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKECXXINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKECHECKCOMPILERFLAGCOMMONPATTERNS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKECOMMONLANGUAGEINCLUDE.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKEDEPENDENTOPTION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKEGENERICSYSTEM.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKEINITIALIZECONFIGS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKELANGUAGEINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKEPACKAGECONFIGHELPERS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKEPARSEIMPLICITLINKINFO.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKERCINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKESYSTEMSPECIFICINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CMAKESYSTEMSPECIFICINITIALIZE.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CHECKCSOURCECOMPILES.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CHECKCSOURCERUNS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CHECKCXXCOMPILERFLAG.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CHECKCXXSOURCECOMPILES.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CHECKINCLUDEFILE.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CHECKINCLUDEFILECXX.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CHECKLIBRARYEXISTS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\CHECKTYPESIZE.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\COMPILER\CMAKECOMMONCOMPILERMACROS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\COMPILER\MSVC-C.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\COMPILER\MSVC-CXX.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\COMPILER\MSVC.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\EXTERNALPROJECT\SHARED_INTERNAL_COMMANDS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FETCHCONTENT.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FETCHCONTENT\CMAKELISTS.CMAKE.IN
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDBOOST.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDGIT.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDOPENMP.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDPACKAGEHANDLESTANDARDARGS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDPACKAGEMESSAGE.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDPKGCONFIG.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDPYTHON.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDPYTHON\SUPPORT.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\FINDTHREADS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\GNUINSTALLDIRS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\INTERNAL\CMAKECLINKERINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\INTERNAL\CMAKECXXLINKERINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\INTERNAL\CMAKECOMMONLINKERINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\INTERNAL\CMAKERCLINKERINFORMATION.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\INTERNAL\CHECKCOMPILERFLAG.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\INTERNAL\CHECKFLAGCOMMONCONFIG.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\INTERNAL\CHECKSOURCECOMPILES.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\INTERNAL\CHECKSOURCERUNS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\LINKER\WINDOWS-MSVC-C.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\LINKER\WINDOWS-MSVC-CXX.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\LINKER\WINDOWS-MSVC.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\WINDOWS-INITIALIZE.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\WINDOWS-MSVC-C.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\WINDOWS-MSVC-CXX.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\WINDOWS-MSVC.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\WINDOWS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\PLATFORM\WINDOWSPATHS.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\TESTBIGENDIAN.CMAKE
C:\PROGRAM FILES\CMAKE\SHARE\CMAKE-3.31\MODULES\WRITEBASICCONFIGVERSIONFILE.CMAKE
