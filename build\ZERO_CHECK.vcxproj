﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>ARM64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{1E7998AD-9450-33F2-A57B-B79BA3E69C25}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\AI\UltraFlexSTT_CPP\include;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\include;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp;C:\AI\UltraFlexSTT_CPP\external\portaudio\include;C:\AI\UltraFlexSTT_CPP\external\json\single_include;C:\AI\UltraFlexSTT_CPP\external\websocketpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\AI\UltraFlexSTT_CPP\include;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\include;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp;C:\AI\UltraFlexSTT_CPP\external\portaudio\include;C:\AI\UltraFlexSTT_CPP\external\json\single_include;C:\AI\UltraFlexSTT_CPP\external\websocketpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\AI\UltraFlexSTT_CPP\include;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\include;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp;C:\AI\UltraFlexSTT_CPP\external\portaudio\include;C:\AI\UltraFlexSTT_CPP\external\json\single_include;C:\AI\UltraFlexSTT_CPP\external\websocketpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\AI\UltraFlexSTT_CPP\include;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\include;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp;C:\AI\UltraFlexSTT_CPP\external\portaudio\include;C:\AI\UltraFlexSTT_CPP\external\json\single_include;C:\AI\UltraFlexSTT_CPP\external\websocketpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\6a3b9035d182910ff64fb35fcf7ea3cf\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/AI/UltraFlexSTT_CPP -BC:/AI/UltraFlexSTT_CPP/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/AI/UltraFlexSTT_CPP/build/UltraFlexSTT.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\AI\UltraFlexSTT_CPP\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeCCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeCXXCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeRCCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeSystem.cmake;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\cmake\gmock.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\cmake\gmock_main.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\Config.cmake.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\gtest.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\gtest_main.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\internal_utils.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\PortAudioConfig.cmake.in;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\cmake_uninstall.cmake.in;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\modules\FindJACK.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\modules\FindRegex.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\portaudio-2.0.pc.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\build-info.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\whisper-config.cmake.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\whisper.pc.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\cmake\common.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\cmake\ggml-config.cmake.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\ggml-cpu\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\ggml-cpu\cmake\FindSIMD.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\tests\CMakeLists.txt;C:\Program Files\CMake\share\cmake-3.31\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckTypeSize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FetchContent.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FetchContent\CMakeLists.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindOpenMP.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPython.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPython\Support.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeRCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\TestBigEndian.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\src\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\src\ggml-cpu\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\src\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\portaudio\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\tests\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\googlemock\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\googletest\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/AI/UltraFlexSTT_CPP -BC:/AI/UltraFlexSTT_CPP/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/AI/UltraFlexSTT_CPP/build/UltraFlexSTT.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\AI\UltraFlexSTT_CPP\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeCCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeCXXCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeRCCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeSystem.cmake;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\cmake\gmock.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\cmake\gmock_main.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\Config.cmake.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\gtest.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\gtest_main.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\internal_utils.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\PortAudioConfig.cmake.in;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\cmake_uninstall.cmake.in;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\modules\FindJACK.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\modules\FindRegex.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\portaudio-2.0.pc.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\build-info.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\whisper-config.cmake.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\whisper.pc.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\cmake\common.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\cmake\ggml-config.cmake.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\ggml-cpu\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\ggml-cpu\cmake\FindSIMD.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\tests\CMakeLists.txt;C:\Program Files\CMake\share\cmake-3.31\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckTypeSize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FetchContent.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FetchContent\CMakeLists.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindOpenMP.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPython.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPython\Support.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeRCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\TestBigEndian.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\src\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\src\ggml-cpu\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\src\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\portaudio\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\tests\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\googlemock\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\googletest\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/AI/UltraFlexSTT_CPP -BC:/AI/UltraFlexSTT_CPP/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/AI/UltraFlexSTT_CPP/build/UltraFlexSTT.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\AI\UltraFlexSTT_CPP\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeCCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeCXXCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeRCCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeSystem.cmake;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\cmake\gmock.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\cmake\gmock_main.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\Config.cmake.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\gtest.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\gtest_main.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\internal_utils.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\PortAudioConfig.cmake.in;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\cmake_uninstall.cmake.in;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\modules\FindJACK.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\modules\FindRegex.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\portaudio-2.0.pc.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\build-info.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\whisper-config.cmake.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\whisper.pc.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\cmake\common.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\cmake\ggml-config.cmake.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\ggml-cpu\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\ggml-cpu\cmake\FindSIMD.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\tests\CMakeLists.txt;C:\Program Files\CMake\share\cmake-3.31\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckTypeSize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FetchContent.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FetchContent\CMakeLists.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindOpenMP.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPython.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPython\Support.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeRCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\TestBigEndian.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\src\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\src\ggml-cpu\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\src\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\portaudio\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\tests\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\googlemock\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\googletest\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/AI/UltraFlexSTT_CPP -BC:/AI/UltraFlexSTT_CPP/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/AI/UltraFlexSTT_CPP/build/UltraFlexSTT.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\AI\UltraFlexSTT_CPP\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeCCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeCXXCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeRCCompiler.cmake;C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\3.31.0-rc2\CMakeSystem.cmake;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\cmake\gmock.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googlemock\cmake\gmock_main.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\Config.cmake.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\gtest.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\gtest_main.pc.in;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-src\googletest\cmake\internal_utils.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\PortAudioConfig.cmake.in;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\cmake_uninstall.cmake.in;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\modules\FindJACK.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\modules\FindRegex.cmake;C:\AI\UltraFlexSTT_CPP\external\portaudio\cmake\portaudio-2.0.pc.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\build-info.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\whisper-config.cmake.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\cmake\whisper.pc.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\cmake\common.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\cmake\ggml-config.cmake.in;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\ggml-cpu\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\ggml\src\ggml-cpu\cmake\FindSIMD.cmake;C:\AI\UltraFlexSTT_CPP\external\whisper.cpp\src\CMakeLists.txt;C:\AI\UltraFlexSTT_CPP\tests\CMakeLists.txt;C:\Program Files\CMake\share\cmake-3.31\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckTypeSize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FetchContent.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FetchContent\CMakeLists.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindOpenMP.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPython.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPython\Support.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeRCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\TestBigEndian.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\AI\UltraFlexSTT_CPP\build\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\src\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\ggml\src\ggml-cpu\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\whisper.cpp\src\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\external\portaudio\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\tests\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\googlemock\CMakeFiles\generate.stamp;C:\AI\UltraFlexSTT_CPP\build\_deps\googletest-build\googletest\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>